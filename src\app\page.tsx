import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/layout/bento-grid";
import ProfessionalH<PERSON>Background from "@/components/layout/ProfessionalHeroBackground";
import { <PERSON><PERSON> } from "@/components/ui/button";
import HeroSection from "@/pages/landing/hero-section-page";
import { ArrowR<PERSON>, FileText, Target, TrendingUp } from "lucide-react";
import Link from "next/link";

export default function Home() {
  // console.log("Homepage");
  return (
    <ProfessionalHeroBackground>
      <main className="min-h-screen">
        <HeroSection />
        {/* Sheet Creation Section */}
        <div className="relative py-20">
          <div
            aria-hidden="true"
            className="absolute -top-20 left-1/2 -translate-x-1/2 h-[400px] w-[400px] bg-gradient-to-br from-blue-900 via-blue-800 to-cyan-700 opacity-25 blur-3xl rounded-full -z-10"
          />
          <div className="relative text-center mx-auto max-w-6xl px-4">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
              Create Personalized Coding Sheets
            </h2>
            <p className="text-lg text-gray-300 mb-12 max-w-3xl mx-auto">
              Transform any Codeforces profile into a structured learning
              experience. Generate custom problem sheets tailored to your skill
              level and track your progress as you master new techniques.
            </p>

            {/* Feature Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-cyan-500/5 rounded-xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="relative bg-slate-900/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 hover:border-blue-500/30 transition-all duration-300">
                  <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mb-4 mx-auto">
                    <Target className="w-6 h-6 text-blue-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-3">
                    Smart Curation
                  </h3>
                  <p className="text-gray-300 text-sm">
                    Automatically organize problems by difficulty rating, tags,
                    and solve status from your Codeforces profile.
                  </p>
                </div>
              </div>

              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-emerald-500/5 rounded-xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="relative bg-slate-900/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 hover:border-green-500/30 transition-all duration-300">
                  <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mb-4 mx-auto">
                    <TrendingUp className="w-6 h-6 text-green-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-3">
                    Progress Tracking
                  </h3>
                  <p className="text-gray-300 text-sm">
                    Sync problems to your codeforces account and track your
                    progress across different difficulty levels and topics.
                  </p>
                </div>
              </div>

              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/5 rounded-xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="relative bg-slate-900/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 hover:border-purple-500/30 transition-all duration-300">
                  <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mb-4 mx-auto">
                    <FileText className="w-6 h-6 text-purple-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-3">
                    <span className="text-lg font-light">Coming soon</span>
                  </h3>
                  <p className="text-gray-300 text-sm">
                    Download your sheets as organized documents or share them
                    with teammates for collaborative practice.
                  </p>
                </div>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/sheetscope">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white font-semibold px-8 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 group hover:cursor-pointer"
                >
                  Create Your First Sheet
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
        <div className="relative py-12">
          <div
            aria-hidden="true"
            className="absolute inset-0 m-auto h-[357px] w-[357px] rounded-full bg-blue-500/30 opacity-20 blur-3xl"
          />
          <div className="relative text-center mx-auto max-w-5xl px-4">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
              Features
            </h2>
            <p className="text-lg text-gray-300 mb-10">
              Explore how our tool visualizes your code execution.
            </p>
            <div className="w-full md:w-5/6 mx-auto">
              <BentoDemo />
            </div>
          </div>
        </div>
      </main>
    </ProfessionalHeroBackground>
  );
}
