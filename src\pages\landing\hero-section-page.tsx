"use client";

import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { useEffect, useState } from "react";

const quotes = [
  "An algorithm must be seen to be believed. — <PERSON>",
  "The function of good software is to make the complex appear to be simple. — <PERSON>",
  "Controlling complexity is the essence of computer programming. — <PERSON>",
  "Data dominates. If you've chosen the right data structures, the algorithms will be self-evident. — <PERSON>",
  "Algorithms are at the heart of computer science, and data structures are the backbone. — <PERSON><PERSON>",
  "Bad programmers worry about the code. Good programmers worry about data structures and their relationships. — <PERSON><PERSON>",
  "The purpose of abstraction is not to be vague, but to create a new semantic level in which one can be absolutely precise. — <PERSON>s<PERSON>",
  "Programs must be written for people to read, and only incidentally for machines to execute. — <PERSON>",
  "Simplicity is a prerequisite for reliability. — <PERSON><PERSON><PERSON>",
  "The most important property of a program is whether it accomplishes the intention of its user. — C.A.R<PERSON>",
];

const HeroSection = () => {
  const [quote, setQuote] = useState("");

  useEffect(() => {
    setQuote(quotes[Math.floor(Math.random() * quotes.length)]);
  }, []);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen text-center px-4 md:px-6">
      <h1 className="text-4xl sm:text-5xl md:text-7xl font-bold tracking-tight text-white mb-4 sm:mb-6">
        Build better skills
      </h1>
      <p className="max-w-xs sm:max-w-xl md:max-w-3xl text-base sm:text-lg md:text-xl text-slate-300 mb-8 sm:mb-10 h-24 sm:h-16">
        {quote}
      </p>
      <div className="flex flex-col sm:flex-row gap-4">
        <Link href="/sheetscope">
          <Button
            variant="outline"
            className="py-3 px-6 rounded-lg border-2 border-slate-400 text-white hover:bg-slate-800 hover:border-slate-300 transition-colors"
          >
            Try for Free
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default HeroSection;
